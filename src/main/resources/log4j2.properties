status=warn
# 将日志输出至控制台和文件
appender.console.type=Console
appender.console.name=Console
appender.console.target=SYSTEM_OUT
appender.console.layout.type=PatternLayout
appender.console.layout.pattern=%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
appender.rolling.type=RollingFile
appender.rolling.name=RollingFile
appender.rolling.fileName=logs/application.log
appender.rolling.filePattern=logs/application-%d{yyyy-MM-dd}-%i.log
appender.rolling.layout.type=PatternLayout
appender.rolling.layout.pattern=%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n
appender.rolling.policies.type=Policies
appender.rolling.policies.size.type=SizeBasedTriggeringPolicy
appender.rolling.policies.size.size=10MB
appender.rolling.policies.time.type=TimeBasedTriggeringPolicy
appender.rolling.policies.time.interval=1
appender.rolling.policies.time.modulate=true
appender.rolling.strategy.type=DefaultRolloverStrategy
appender.rolling.strategy.max=10
# Root Logger
rootLogger.level=info
rootLogger.appenderRef.console.ref=Console
rootLogger.appenderRef.rolling.ref=RollingFile
# ClickHouse Connector Logger
logger.clickhouse.name=org.apache.flink.connector.clickhouse
logger.clickhouse.level=debug
logger.clickhouse.additivity=false
logger.clickhouse.appenderRef.console.ref=Console
logger.clickhouse.appenderRef.rolling.ref=RollingFile
