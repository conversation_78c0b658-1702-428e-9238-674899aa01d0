package org.apache.flink.connector.clickhouse.ck.strategy;

import java.io.Serial;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.flink.connector.clickhouse.ck.client.NodeClient;

/** 轮询策略实现。 按照顺序轮流选择可用的节点客户端。 */
public class RoundRobinStrategy implements NodeSelectionStrategy {

  @Serial private static final long serialVersionUID = -1602115368987746009L;

  /** 轮询计数器 */
  private final AtomicInteger counter = new AtomicInteger(0);

  @Override
  public NodeClient selectNode(List<NodeClient> clients) {
    if (clients == null || clients.isEmpty()) {
      return null;
    }

    // 原子操作获取下一个索引值
    int size = clients.size();
    int index = Math.abs(counter.getAndIncrement() % size);

    return clients.get(index);
  }

  @Override
  public String getName() {
    return "RoundRobin";
  }
}
