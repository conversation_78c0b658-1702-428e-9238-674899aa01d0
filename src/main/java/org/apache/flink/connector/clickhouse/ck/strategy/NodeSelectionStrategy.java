package org.apache.flink.connector.clickhouse.ck.strategy;

import java.io.Serializable;
import java.util.List;
import org.apache.flink.connector.clickhouse.ck.client.NodeClient;

/** ClickHouse节点选择策略接口。 定义如何从多个可用节点中选择一个节点的策略。 */
public interface NodeSelectionStrategy extends Serializable {

  /**
   * 从可用节点列表中选择一个节点。
   *
   * @param clients 可用节点客户端列表
   * @return 选择的节点客户端，如果没有可用节点则返回null
   */
  NodeClient selectNode(List<NodeClient> clients);

  /**
   * 获取策略名称。
   *
   * @return 策略名称
   */
  String getName();
}
