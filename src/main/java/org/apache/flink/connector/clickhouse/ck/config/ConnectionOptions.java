package org.apache.flink.connector.clickhouse.ck.config;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/** 用于配置 ClickHouse 连接的选项类。 */
@Getter
@Builder
public class ConnectionOptions implements Serializable {

  @Serial private static final long serialVersionUID = -950863155055135638L;

  /** ClickHouse 服务器主机，多个主机以逗号分隔。 */
  private final String hosts;

  /** ClickHouse 服务器端口。 */
  private final int port;

  /** 用户名。 */
  private final String username;

  /** 密码。 */
  private final String password;

  /** 表所在的集群名称（可选）。 */
  private final String cluster;

  /** 是否使用 SSL 连接。 */
  private final boolean useSSL;

  /** 客户端证书路径，用于 SSL 认证。 */
  private final String clientCertPath;

  /** 客户端密钥路径，用于 SSL 认证。 */
  private final String clientKeyPath;

  /** 其他连接选项。 */
  @Builder.Default private final Map<String, String> additionalOptions = new HashMap<>();
}
