package org.apache.flink.connector.clickhouse.ck.client;

import com.clickhouse.client.api.Client;
import java.io.Closeable;
import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/** ClickHouse节点客户端封装类。 封装了对单个ClickHouse节点的Client实例以及相关节点信息。 */
@Slf4j
public class NodeClient implements Closeable, Serializable {

  @Serial private static final long serialVersionUID = -4774727787775909716L;

  /** 最大连续失败次数，超过此值将标记节点为不可用 */
  private static final int MAX_FAILURES = 3;

  /** 节点主机名 */
  @Getter private final String host;

  /** 节点端口 */
  @Getter private final int port;

  /** ClickHouse客户端实例 */
  @Getter private final Client client;

  /** 节点是否可用 */
  private final AtomicBoolean available = new AtomicBoolean(true);

  /** 连续失败次数 */
  private final AtomicInteger failureCount = new AtomicInteger(0);

  /** 已关闭标志 */
  private final AtomicBoolean closed = new AtomicBoolean(false);

  /** 节点权重，用于加权选择 */
  @Getter private volatile double weight = 1.0;

  /**
   * 创建一个新的ClickHouse节点客户端
   *
   * @param host 主机名
   * @param port 端口
   * @param client ClickHouse客户端实例
   */
  public NodeClient(String host, int port, Client client) {
    this.host = host;
    this.port = port;
    this.client = client;
  }

  /**
   * 更新此节点的权重
   *
   * @param weight 新权重值
   */
  public void updateWeight(double weight) {
    this.weight = weight;
  }

  /**
   * 获取节点是否可用
   *
   * @return 如果节点可用返回true，否则返回false
   */
  public boolean isAvailable() {
    return available.get() && !closed.get();
  }

  /** 标记节点为可用 */
  public void markAvailable() {
    failureCount.set(0);
    available.set(true);
  }

  /**
   * 标记节点操作失败
   *
   * @return 连续失败次数
   */
  public int markFailure() {
    int failures = failureCount.incrementAndGet();
    if (failures >= MAX_FAILURES) {
      available.set(false);
      log.warn("节点 {}:{} 已被标记为不可用，连续失败次数: {}", host, port, failures);
    }
    return failures;
  }

  /**
   * 执行健康检查
   *
   * @return 如果健康检查通过返回true，否则返回false
   */
  public boolean healthCheck() {
    if (closed.get()) {
      return false;
    }

    try {
      // 执行简单查询检查连接是否正常
      client.queryAll("SELECT 1");
      markAvailable();
      return true;
    } catch (Exception e) {
      log.warn("节点 {}:{} 健康检查失败: {}", host, port, e.getMessage());
      markFailure();
      return false;
    }
  }

  @Override
  public void close() throws IOException {
    if (closed.compareAndSet(false, true)) {
      try {
        client.close();
        log.info("节点 {}:{} 客户端已关闭", host, port);
      } catch (Exception e) {
        log.error("关闭节点 {}:{} 客户端时出错", host, port, e);
        throw new IOException("关闭客户端出错", e);
      }
    }
  }

  @Override
  public String toString() {
    return "NodeClient{"
        + "host='"
        + host
        + '\''
        + ", port="
        + port
        + ", available="
        + available.get()
        + ", weight="
        + weight
        + ", closed="
        + closed.get()
        + '}';
  }
}
