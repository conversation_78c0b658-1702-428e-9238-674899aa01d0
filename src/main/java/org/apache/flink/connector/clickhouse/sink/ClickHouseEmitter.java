package org.apache.flink.connector.clickhouse.sink;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.clickhouse.config.ClickHouseSinkOptions;
import org.dromara.hutool.core.thread.ThreadUtil;
import org.jctools.queues.MpscGrowableArrayQueue;

/**
 * ClickHouse数据发射器的默认实现，用于将数据写入ClickHouse。
 * 采用单例模式，确保每个表只有一个emitter实例。
 * 继承Thread类，自身作为刷新线程运行。
 *
 * @param <T> 写入的数据类型
 */
@Slf4j
public class ClickHouseEmitter<T> extends Thread {

  // 表名到emitter实例的映射，确保每个表只有一个emitter实例
  private static final Map<String, ClickHouseEmitter<?>> INSTANCES = new ConcurrentHashMap<>();
  // 最大队列容量
  private static final int MAX_QUEUE_CAPACITY = 50_0000;
  // 初始队列容量
  private static final int INITIAL_QUEUE_CAPACITY = 65536; // 2^16
  // 队列阈值，当队列中的记录数达到此值时，尝试刷新队列
  private static final int QUEUE_THRESHOLD = 10_0000;
  // 批处理列表池的最大大小
  private static final int BATCH_LIST_POOL_SIZE = 3;

  private final ClickHouseSinkOptions sinkOptions;
  private final String databaseName;
  private final String tableName;
  private final int batchSize = 30000;
  private final long flushIntervalMs = 1000;
  // 内存队列，用于缓存待写入的数据 - 线程安全的多生产者单消费者队列
  private final MpscGrowableArrayQueue<T> queue;
  // 当前队列中的记录数
  private final AtomicLong pendingRecords = new AtomicLong(0);
  // 批处理列表对象池，用于重用ArrayList对象
  private final ConcurrentLinkedQueue<List<T>> batchListPool = new ConcurrentLinkedQueue<>();
  // 上次刷新时间
  private volatile long lastFlushTime;
  // 是否正在运行
  private volatile boolean isRunning = true;

  /**
   * 私有构造函数，防止外部直接创建实例。
   *
   * @param sinkOptions Sink选项
   */
  private ClickHouseEmitter(ClickHouseSinkOptions sinkOptions) {
    super("ClickHouse-Emitter-" + sinkOptions.getTableOptions().getName());

    this.sinkOptions = sinkOptions;
    this.databaseName = sinkOptions.getTableOptions().getDatabase();
    this.tableName = sinkOptions.getTableOptions().getName();

    // 初始化内存队列，使用JCTools的MpscGrowableArrayQueue
    // 初始容量为16384，可以动态增长到MAX_QUEUE_CAPACITY
    this.queue = new MpscGrowableArrayQueue<>(INITIAL_QUEUE_CAPACITY, MAX_QUEUE_CAPACITY);

    // 预先创建几个批处理列表放入对象池
    for (int i = 0; i < BATCH_LIST_POOL_SIZE; i++) {
      batchListPool.offer(new ArrayList<>(batchSize));
    }

    // 记录初始刷新时间
    this.lastFlushTime = System.currentTimeMillis();

    // 设置为守护线程
    setDaemon(true);

    // 启动线程
    start();

    log.info("表 {} 的ClickHouse emitter已启动", tableName);
  }

  /**
   * 获取指定表的ClickHouseEmitter实例，如果不存在则创建。
   *
   * @param <T>         数据类型
   * @param sinkOptions Sink选项
   * @return ClickHouseEmitter实例
   */
  @SuppressWarnings("unchecked")
  public static <T> ClickHouseEmitter<T> getInstance(ClickHouseSinkOptions sinkOptions) {
    String tableName = sinkOptions.getTableOptions().getName();
    return (ClickHouseEmitter<T>) INSTANCES.computeIfAbsent(tableName, k -> new ClickHouseEmitter<>(sinkOptions));
  }

  /**
   * 检查emitter是否正在运行，如果已关闭则抛出异常
   *
   * @throws IllegalStateException 如果emitter已关闭
   */
  private void checkRunning() throws IllegalStateException {
    if (!isRunning) {
      throw new IllegalStateException("表 " + tableName + " 的ClickHouse emitter已关闭");
    }
  }

  /**
   * 从对象池中获取一个批处理列表，如果池为空则创建一个新的
   *
   * @return 批处理列表
   */
  private List<T> getBatchList() {
    List<T> batch = batchListPool.poll();
    if (batch == null) {
      // 如果对象池为空，创建一个新的列表
      batch = new ArrayList<>(batchSize);
      if (log.isDebugEnabled()) {
        log.debug("表 {} 创建了新的批处理列表，当前对象池大小: {}", tableName, batchListPool.size());
      }
    } else if (!batch.isEmpty()) {
      // 如果列表不为空，清空列表
      batch.clear();
    }

    return batch;
  }

  /**
   * 将使用完的批处理列表放回对象池
   *
   * @param batch 使用完的批处理列表
   */
  private void returnBatchList(List<T> batch) {
    if (batch != null) {
      batch.clear();  // 确保列表是空的
      batchListPool.offer(batch);
    }
  }

  /**
   * 线程运行方法，持续检查队列状态并执行刷新操作
   */
  @Override
  public void run() {
    try {
      while (isRunning) {
        try {
          // 检查是否需要刷新
          checkAndFlush();
          // 短暂休眠，避免CPU占用过高
          Thread.sleep(10);
        } catch (Exception e) {
          if (isRunning) {
            log.error("表 {} 的刷新线程执行出错", tableName, e);
          }
        }
      }

      // 线程结束前执行最后一次刷新
      try {
        flushInternal();
      } catch (Exception e) {
        log.error("表 {} 的刷新线程结束前最后刷新失败", tableName, e);
      }

    } catch (Throwable t) {
      log.error("表 {} 的刷新线程异常退出", tableName, t);
    }
  }

  /**
   * 检查是否需要刷新数据，基于数据量和时间两个维度
   */
  private void checkAndFlush() throws Exception {
    long currentTime = System.currentTimeMillis();
    long currentPending = pendingRecords.get();

    // 如果队列为空，不需要刷新
    if (currentPending <= 0) {
      return;
    }

    // 满足以下任一条件时执行刷新：
    // 1. 队列中的记录数达到批量大小
    // 2. 距离上次刷新已经超过了指定的时间间隔
    // 3. 队列中的记录数达到了阈值
    boolean shouldFlush =
        currentPending >= batchSize || currentTime - lastFlushTime >= flushIntervalMs;

    if (shouldFlush) {
      flushInternal();
    }
  }

  /**
   * 发送单条记录到内存队列
   *
   * @param record 记录
   * @throws Exception 如果队列已满或其他异常
   */
  public void emit(T record) throws Exception {
    // 检查是否正在运行
    checkRunning();

    int i = 0;
    while (pendingRecords.get() >= QUEUE_THRESHOLD) {
      checkRunning();
      if (i++ < 1_0000) {
        onSpinWait();
      } else {
        ThreadUtil.safeSleep(1);
      }
    }

    doEmit(record);
  }

  private void doEmit(T record) {
    boolean writeSuccess;
    do {
      writeSuccess = queue.offer(record);
      if (writeSuccess) {
        onSpinWait();
      }

      checkRunning();
    } while (!writeSuccess);

    // 更新计数
    pendingRecords.incrementAndGet();
  }

  /**
   * 手动触发刷新操作
   *
   * @throws Exception 如果刷新失败
   */
  public void flush() throws Exception {
    flushInternal();
  }

  /** 内部刷新实现，从队列取出数据并写入ClickHouse */
  private synchronized void flushInternal() {
    // 快速检查，避免不必要的操作
    if (pendingRecords.get() <= 0) {
      return;
    }

    // 从对象池获取批处理列表
    List<T> batch = getBatchList();

    try {
      // 从队列中取出数据
      T record;
      while (batch.size() < batchSize && (record = queue.poll()) != null) {
        batch.add(record);
      }

      // 更新最后刷新时间
      lastFlushTime = System.currentTimeMillis();

      if (batch.isEmpty()) {
        return;
      }

      // 更新计数器
      pendingRecords.addAndGet(-batch.size());

      // 处理数据写入
      try {
        // TODO: 实际写入ClickHouse的逻辑
        log.debug("成功写入 {} 条数据到表 {}.{}", batch.size(), databaseName, tableName);
      } catch (Exception e) {
        log.error("写入数据到表 {}.{} 失败", databaseName, tableName, e);
        throw e;
      }
    } finally {
      // 将批处理列表放回对象池
      returnBatchList(batch);
    }
  }

  /** 关闭发射器，刷新剩余数据并释放资源 */
  public synchronized void close() {
    if (!isRunning) {
      log.warn("表 {} 的ClickHouse emitter已关闭", tableName);
      return;
    }

    log.info("正在关闭表 {} 的ClickHouse emitter", tableName);
    isRunning = false;

    // 等待线程结束
    try {
      join(5000);
    } catch (InterruptedException e) {
      log.warn("等待表 {} 的刷新线程结束被中断", tableName, e);
      Thread.currentThread().interrupt();
    }

    // 如果线程仍在运行，尝试中断
    if (isAlive()) {
      interrupt();
      log.warn("表 {} 的刷新线程未能正常结束，已强制中断", tableName);
    }

    // 从实例映射中移除
    INSTANCES.remove(tableName);
    log.info("已关闭表 {} 的ClickHouse emitter", tableName);
  }
}
