package org.apache.flink.connector.clickhouse.config;

import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;

/** ClickHouse 表相关配置选项。 */
@Getter
@Builder
public class ClickHouseTableOptions implements Serializable {

  @Serial private static final long serialVersionUID = -2358427631055135638L;

  /** 目标表名。 */
  private final String name;

  /** 目标库名（可选，如果为null则使用连接默认数据库）。 */
  private final String database;

  /** 是否使用加权写入（根据磁盘空间）。 */
  @Builder.Default private final boolean useWeightedWrite = false;
}
