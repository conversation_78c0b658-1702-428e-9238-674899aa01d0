package org.apache.flink.connector.clickhouse.utils;

import static org.dromara.hutool.core.text.StrValidator.isAllNotBlank;

import com.clickhouse.client.api.Client;
import com.clickhouse.client.api.ConnectionReuseStrategy;
import com.clickhouse.client.api.query.GenericRecord;
import com.clickhouse.data.ClickHouseColumn;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.clickhouse.ck.entity.NodeInfo;
import org.apache.flink.connector.clickhouse.ck.entity.TableMeta;
import org.dromara.hutool.core.lang.Assert;
import org.dromara.hutool.core.util.RuntimeUtil;

/** ClickHouse 相关工具类。 */
@Slf4j
public class ClientUtil {

  /**
   * 创建ClickHouse客户端。
   *
   * @param hosts ClickHouse 服务器主机，多个主机以逗号分隔
   * @param port ClickHouse 服务器端口
   * @param username 用户名
   * @param password 密码
   */
  public static Client createClickHouseClient(
      String hosts, int port, String username, String password) {
    return createClickHouseClient(hosts, port, username, password, false, null, null, null);
  }

  /**
   * 创建ClickHouse客户端。
   *
   * @param hosts ClickHouse 服务器主机，多个主机以逗号分隔
   * @param port ClickHouse 服务器端口
   * @param username 用户名
   * @param password 密码
   * @return ClickHouse客户端
   */
  public static Client createClickHouseClient(
      String hosts, int port, String username, String password, Map<String, String> options) {
    return createClickHouseClient(hosts, port, username, password, false, null, null, options);
  }

  /**
   * 创建ClickHouse客户端。
   *
   * @param hosts ClickHouse 服务器主机，多个主机以逗号分隔
   * @param port ClickHouse 服务器端口
   * @param username 用户名
   * @param password 密码
   * @param useSSL 是否使用SSL连接
   * @param clientCertPath 客户端证书路径
   * @param clientKeyPath 客户端密钥路径
   * @param options 其他连接选项
   * @return ClickHouse客户端
   */
  public static Client createClickHouseClient(
      String hosts,
      int port,
      String username,
      String password,
      boolean useSSL,
      String clientCertPath,
      String clientKeyPath,
      Map<String, String> options) {
    // 如果开启 useSSL， clientCertPath 和 clientKeyPath 不能为空
    Assert.isTrue(!useSSL || isAllNotBlank(clientCertPath, clientKeyPath), "SSL 连接需要配置证书和密钥");

    // 初始化 ClickHouse 客户端构建器
    Client.Builder builder = new Client.Builder();

    // 1. 配置端点（支持多主机）
    for (String host : hosts.split(",")) {
      builder.addEndpoint("http://" + host.trim() + ":" + port + "/");
    }

    // 2. 配置认证信息
    builder.setUsername(username).setPassword(password);

    // 4. 配置 SSL 连接
    if (useSSL) {
      builder.useSSLAuthentication(true);
      builder.setClientCertificate(clientCertPath).setClientKey(clientKeyPath);
    }

    // 5. 添加其他自定义配置
    Optional.ofNullable(options).ifPresent(builder::setOptions);

    // 6. 这里进行强制参数覆盖
    builder
        // 开启连接池
        .enableConnectionPool(true)
        // 连接超时 15 秒
        .setConnectTimeout(15, ChronoUnit.SECONDS)
        // 最大连接数 核心数 + 1
        .setMaxConnections(RuntimeUtil.getProcessorCount() + 1)
        // 连接池连接超时 5 分钟
        .setConnectionTTL(5, ChronoUnit.MINUTES)
        // 读取超时 1 分钟
        .setSocketTimeout(1, ChronoUnit.MINUTES)
        // 连接请求超时 5 秒
        .setConnectionRequestTimeout(5, ChronoUnit.SECONDS)
        // 连接重用策略 FIFO
        .setConnectionReuseStrategy(ConnectionReuseStrategy.FIFO)
        // 开启连接保持
        .setSocketKeepAlive(true)
        // 连接保持超时 3 分钟
        .setKeepAliveTimeout(3, ChronoUnit.MINUTES)
        // 默认执行超时 1 分钟
        .setExecutionTimeout(1, ChronoUnit.MINUTES)
        // 使用异步请求
        .useAsyncRequests(true);

    // 构建并返回客户端
    return builder.build();
  }

  /**
   * 检查表是否存在，如果不存在则抛出异常。
   *
   * @param client ClickHouse 客户端
   * @param database 数据库名
   * @param tableName 表名
   */
  public static void checkTableExists(Client client, String database, String tableName) {
    String query =
        String.format(
            "SELECT count() FROM system.tables WHERE database = '%s' AND name = '%s'",
            database, tableName);

    List<GenericRecord> records = client.queryAll(query);
    if (records.isEmpty()) {
      throw new IllegalStateException("无法获取表信息");
    }

    GenericRecord record = records.get(0);
    Long count = Long.valueOf(record.getString(0));

    if (count == 0) {
      throw new IllegalStateException(
          String.format("Table %s.%s does not exist in ClickHouse", database, tableName));
    }
  }

  /**
   * 获取表的结构信息。
   *
   * @param client ClickHouse 客户端
   * @param database 数据库名
   * @param tableName 表名
   * @return 表结构的描述
   */
  public static TableMeta getTableStructure(Client client, String database, String tableName) {
    String query = String.format("DESCRIBE TABLE %s.%s", database, tableName);

    List<GenericRecord> records = client.queryAll(query);

    List<ClickHouseColumn> columnList =
        records.stream()
            .map(record -> ClickHouseColumn.of(record.getString("name"), record.getString("type")))
            .toList();

    return TableMeta.builder()
        .database(database)
        .table(tableName)
        .columns(columnList)
        .columnTypes(
            columnList.stream()
                .collect(
                    Collectors.toMap(
                        ClickHouseColumn::getColumnName, ClickHouseColumn::getDataType)))
        .build();
  }

  /**
   * 查询ClickHouse集群节点信息。
   *
   * @param client ClickHouse 客户端
   * @param clusterName 集群名称
   * @return 集群节点信息列表
   */
  public static List<NodeInfo> queryClusterNodes(Client client, String clusterName) {
    if (clusterName == null || clusterName.isEmpty()) {
      return new ArrayList<>();
    }

    String query =
        "SELECT shard_num, host_name, port "
            + "FROM system.clusters "
            + "WHERE cluster = '"
            + clusterName
            + "'";

    try {
      List<GenericRecord> records = client.queryAll(query);
      List<NodeInfo> nodes = new ArrayList<>();

      for (GenericRecord record : records) {
        String host = record.getString("host_name");
        int port = Integer.parseInt(record.getString("port"));
        int shardNum = Integer.parseInt(record.getString("shard_num"));

        NodeInfo nodeInfo = NodeInfo.builder().host(host).port(port).shardNum(shardNum).build();

        nodes.add(nodeInfo);
      }

      return nodes;
    } catch (Exception e) {
      log.error("查询集群 {} 节点信息时出错", clusterName, e);
      return new ArrayList<>();
    }
  }
}
