package org.apache.flink.connector.clickhouse.ck.entity;

import java.io.Serial;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** ClickHouse节点信息实体类。 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NodeInfo implements Serializable {

  @Serial private static final long serialVersionUID = -5086044498547594672L;

  /** 主机名 */
  private String host;

  /** 端口号 */
  private int port;

  /** 分片号 */
  private int shardNum;
}
