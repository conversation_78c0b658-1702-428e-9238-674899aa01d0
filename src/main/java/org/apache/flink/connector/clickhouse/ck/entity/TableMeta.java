package org.apache.flink.connector.clickhouse.ck.entity;

import com.clickhouse.data.ClickHouseColumn;
import com.clickhouse.data.ClickHouseDataType;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 表字段信息.
 *
 * <AUTHOR>
 * @date 2025/5/29 16:23:00
 */
@Data
@Builder
@Accessors(chain = true)
public class TableMeta implements Serializable {
  @Serial private static final long serialVersionUID = -1114247816986559029L;

  /** 数据库名称 */
  private final String database;

  /** 表名称 */
  private final String table;

  /** 表字段列表 */
  private final List<ClickHouseColumn> columns;

  /** 表字段类型列表 */
  private final Map<String, ClickHouseDataType> columnTypes;
}
