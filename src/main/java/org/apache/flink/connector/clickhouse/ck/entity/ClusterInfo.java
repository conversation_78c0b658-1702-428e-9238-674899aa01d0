package org.apache.flink.connector.clickhouse.ck.entity;

import com.clickhouse.client.api.Client;
import com.clickhouse.client.api.query.GenericRecord;
import java.io.Closeable;
import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.clickhouse.ck.client.NodeClient;
import org.apache.flink.connector.clickhouse.ck.config.ConnectionOptions;
import org.apache.flink.connector.clickhouse.utils.ClientUtil;

/** ClickHouse集群信息管理类。 用于获取集群节点信息、节点磁盘空间信息等。 */
@Slf4j
public class ClusterInfo implements Closeable, Serializable {

  /** 默认刷新间隔（毫秒） */
  private static final long DEFAULT_REFRESH_INTERVAL_MS = 60000;

  /** 字节到GB的转换常量 (1GB = 10^9 字节) */
  private static final double BYTES_TO_GB = 1_000_000_000.0;

  @Serial private static final long serialVersionUID = 1768226892589860315L;

  /** 集群名称 */
  @Getter private final String clusterName;

  /** 连接选项 */
  private final ConnectionOptions connectionOptions;

  /** 主要的客户端，用于查询集群信息 */
  private final Client mainClient;

  /** 节点客户端映射，key为节点地址（host:port） */
  private final Map<String, NodeClient> nodeClients = new ConcurrentHashMap<>();

  /** 节点磁盘空间信息缓存，key为节点地址（host:port），值为磁盘空闲空间（GB） */
  private final Map<String, Long> diskSpaceCache = new ConcurrentHashMap<>();

  /** 定时刷新线程池 */
  private final ScheduledExecutorService scheduler;

  /** 是否已关闭 */
  private volatile boolean closed = false;

  /**
   * 创建一个新的ClickHouse集群信息管理器
   *
   * @param clusterName 集群名称
   * @param connectionOptions 连接选项
   * @param refreshIntervalMs 刷新间隔（毫秒）
   */
  public ClusterInfo(
      String clusterName, ConnectionOptions connectionOptions, long refreshIntervalMs) {
    this.clusterName = clusterName;
    this.connectionOptions = connectionOptions;

    // 创建主客户端
    this.mainClient =
        ClientUtil.createClickHouseClient(
            connectionOptions.getHosts(),
            connectionOptions.getPort(),
            connectionOptions.getUsername(),
            connectionOptions.getPassword(),
            connectionOptions.isUseSSL(),
            connectionOptions.getClientCertPath(),
            connectionOptions.getClientKeyPath(),
            connectionOptions.getAdditionalOptions());

    // 初始化集群信息
    try {
      initializeClusterInfo();
    } catch (Exception e) {
      log.error("初始化集群信息失败", e);
    }

    // 设置定时刷新任务
    this.scheduler =
        Executors.newSingleThreadScheduledExecutor(
            r -> {
              Thread t = new Thread(r, "clickhouse-cluster-info-refresh");
              t.setDaemon(true);
              return t;
            });

    scheduler.scheduleAtFixedRate(
        this::refreshClusterInfo, refreshIntervalMs, refreshIntervalMs, TimeUnit.MILLISECONDS);
  }

  /**
   * 使用默认刷新间隔创建ClickHouse集群信息管理器
   *
   * @param clusterName 集群名称
   * @param connectionOptions 连接选项
   */
  public ClusterInfo(String clusterName, ConnectionOptions connectionOptions) {
    this(clusterName, connectionOptions, DEFAULT_REFRESH_INTERVAL_MS);
  }

  /** 初始化集群信息 */
  private void initializeClusterInfo() {
    refreshClusterNodes();
    refreshDiskSpaceInfo();
  }

  /** 刷新集群信息 */
  private void refreshClusterInfo() {
    if (closed) {
      return;
    }

    try {
      refreshClusterNodes();
      refreshDiskSpaceInfo();
      performHealthChecks();
    } catch (Exception e) {
      log.error("刷新集群信息时出错", e);
    }
  }

  /** 刷新集群节点信息 */
  private void refreshClusterNodes() {
    if (clusterName == null || clusterName.isEmpty()) {
      // 如果没有指定集群名称，则只使用连接选项中的主机
      String[] hosts = connectionOptions.getHosts().split(",");
      int port = connectionOptions.getPort();

      for (String host : hosts) {
        String hostTrimmed = host.trim();
        String nodeKey = hostTrimmed + ":" + port;

        if (!nodeClients.containsKey(nodeKey)) {
          try {
            Client client =
                ClientUtil.createClickHouseClient(
                    hostTrimmed,
                    port,
                    connectionOptions.getUsername(),
                    connectionOptions.getPassword(),
                    connectionOptions.isUseSSL(),
                    connectionOptions.getClientCertPath(),
                    connectionOptions.getClientKeyPath(),
                    connectionOptions.getAdditionalOptions());

            NodeClient nodeClient = new NodeClient(hostTrimmed, port, client);
            nodeClients.put(nodeKey, nodeClient);
            log.info("添加节点客户端: {}", nodeKey);
          } catch (Exception e) {
            log.error("为节点 {} 创建客户端失败", nodeKey, e);
          }
        }
      }
      return;
    }

    // 如果指定了集群名称，则查询集群节点信息
    try {
      // 使用ClientUtil查询集群节点信息
      List<NodeInfo> nodes = ClientUtil.queryClusterNodes(mainClient, clusterName);
      Map<String, NodeClient> newNodeClients = new HashMap<>();

      for (NodeInfo nodeInfo : nodes) {
        String host = nodeInfo.getHost();
        int port = nodeInfo.getPort();
        String nodeKey = host + ":" + port;

        // 复用已存在的客户端
        if (nodeClients.containsKey(nodeKey)) {
          newNodeClients.put(nodeKey, nodeClients.get(nodeKey));
          continue;
        }

        // 创建新的客户端
        try {
          Client client =
              ClientUtil.createClickHouseClient(
                  host,
                  port,
                  connectionOptions.getUsername(),
                  connectionOptions.getPassword(),
                  connectionOptions.isUseSSL(),
                  connectionOptions.getClientCertPath(),
                  connectionOptions.getClientKeyPath(),
                  connectionOptions.getAdditionalOptions());

          NodeClient nodeClient = new NodeClient(host, port, client);
          newNodeClients.put(nodeKey, nodeClient);
          log.info("添加集群 {} 的节点客户端: {}", clusterName, nodeKey);
        } catch (Exception e) {
          log.error("为集群 {} 的节点 {} 创建客户端失败", clusterName, nodeKey, e);
        }
      }

      // 关闭不再使用的客户端
      for (Map.Entry<String, NodeClient> entry : nodeClients.entrySet()) {
        if (!newNodeClients.containsKey(entry.getKey())) {
          try {
            entry.getValue().close();
            log.info("关闭不再使用的节点客户端: {}", entry.getKey());
          } catch (Exception e) {
            log.error("关闭节点客户端 {} 时出错", entry.getKey(), e);
          }
        }
      }

      // 更新节点客户端映射
      nodeClients.clear();
      nodeClients.putAll(newNodeClients);

    } catch (Exception e) {
      log.error("刷新集群 {} 节点信息时出错", clusterName, e);
    }
  }

  /** 刷新所有节点的磁盘空间信息 */
  private void refreshDiskSpaceInfo() {
    for (NodeClient nodeClient : nodeClients.values()) {
      if (!nodeClient.isAvailable()) {
        continue;
      }

      try {
        String query = "SELECT free_space FROM system.disks ORDER BY name LIMIT 1";
        List<GenericRecord> records = nodeClient.getClient().queryAll(query);

        if (!records.isEmpty()) {
          GenericRecord record = records.get(0);
          Long freeSpaceBytes = record.getLong(0);
          // 将字节转换为GB
          Long freeSpaceGB = Math.round(freeSpaceBytes / BYTES_TO_GB);
          String nodeKey = nodeClient.getHost() + ":" + nodeClient.getPort();
          diskSpaceCache.put(nodeKey, freeSpaceGB);
          log.debug("更新节点 {} 的磁盘空闲空间: {} GB", nodeKey, freeSpaceGB);
        }
      } catch (Exception e) {
        log.warn(
            "获取节点 {}:{} 的磁盘空间信息失败: {}", nodeClient.getHost(), nodeClient.getPort(), e.getMessage());
        nodeClient.markFailure();
      }
    }

    // 计算并更新节点权重
    updateNodeWeights();
  }

  /** 更新节点权重，基于磁盘空闲空间 */
  private void updateNodeWeights() {
    if (diskSpaceCache.isEmpty()) {
      return;
    }

    double totalFreeSpace = diskSpaceCache.values().stream().mapToDouble(Long::doubleValue).sum();
    if (totalFreeSpace <= 0) {
      return;
    }

    for (NodeClient nodeClient : nodeClients.values()) {
      String nodeKey = nodeClient.getHost() + ":" + nodeClient.getPort();
      Long freeSpace = diskSpaceCache.getOrDefault(nodeKey, 0L);

      // 计算权重：节点的空闲空间占总空闲空间的比例
      double weight = freeSpace / totalFreeSpace;

      // 更新节点权重
      nodeClient.updateWeight(weight);
      log.debug("更新节点 {} 的权重: {} (基于 {} GB 空闲空间)", nodeKey, weight, freeSpace);
    }
  }

  /** 执行所有节点的健康检查 */
  private void performHealthChecks() {
    for (NodeClient nodeClient : nodeClients.values()) {
      if (!nodeClient.isAvailable()) {
        // 尝试恢复不可用的节点
        nodeClient.healthCheck();
      }
    }
  }

  /**
   * 获取所有可用的节点客户端
   *
   * @return 可用节点客户端列表
   */
  public List<NodeClient> getAvailableNodeClients() {
    return nodeClients.values().stream()
        .filter(NodeClient::isAvailable)
        .collect(Collectors.toList());
  }

  /**
   * 获取所有节点客户端
   *
   * @return 所有节点客户端列表
   */
  public List<NodeClient> getAllNodeClients() {
    return new ArrayList<>(nodeClients.values());
  }

  /**
   * 获取节点的空闲磁盘空间
   *
   * @param host 主机名
   * @param port 端口
   * @return 空闲磁盘空间（GB），如果节点不存在则返回0
   */
  public long getNodeFreeDiskSpace(String host, int port) {
    String nodeKey = host + ":" + port;
    return diskSpaceCache.getOrDefault(nodeKey, 0L);
  }

  /**
   * 获取节点的磁盘空间信息映射
   *
   * @return 节点磁盘空间信息映射，键为节点地址，值为空闲空间（GB）
   */
  public Map<String, Long> getDiskSpaceInfo() {
    return Collections.unmodifiableMap(diskSpaceCache);
  }

  @Override
  public void close() throws IOException {
    if (closed) {
      return;
    }
    closed = true;

    // 关闭定时器
    if (scheduler != null && !scheduler.isShutdown()) {
      scheduler.shutdown();
      try {
        if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
          scheduler.shutdownNow();
        }
      } catch (InterruptedException e) {
        scheduler.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }

    // 关闭所有节点客户端
    for (NodeClient nodeClient : nodeClients.values()) {
      try {
        nodeClient.close();
      } catch (Exception e) {
        log.error("关闭节点客户端时出错", e);
      }
    }
    nodeClients.clear();

    // 关闭主客户端
    try {
      mainClient.close();
    } catch (Exception e) {
      log.error("关闭主客户端时出错", e);
    }
  }
}
