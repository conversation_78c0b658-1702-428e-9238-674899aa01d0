package org.apache.flink.connector.clickhouse.config;

import com.clickhouse.data.ClickHouseFormat;
import java.io.Serial;
import java.io.Serializable;
import lombok.Builder;
import lombok.Getter;
import org.apache.flink.connector.clickhouse.ck.config.ConnectionOptions;
import org.dromara.hutool.core.lang.Assert;

/** ClickHouse Sink 的配置选项。 封装了连接配置、表配置和写入行为配置。 */
@Getter
@Builder
public class ClickHouseSinkOptions implements Serializable {

  @Serial private static final long serialVersionUID = -8573573788535292154L;

  private final ConnectionOptions connectionOptions;
  private final ClickHouseTableOptions tableOptions;

  /** 是否写入本地表. */
  @Builder.Default private final boolean writeLocalTable = false;

  /** 写入数据格式。目前仅支持 JSONEachRow。 */
  @Builder.Default private final ClickHouseFormat dataFormat = ClickHouseFormat.JSONEachRow;

  /** 是否启用压缩。 */
  @Builder.Default private final boolean enableCompression = false;

  public void validate() {
    // useWeightedWrite 必须再开启 writeLocalTable 之后才能启用
    Assert.isTrue(
        !tableOptions.isUseWeightedWrite() || writeLocalTable,
        "ClickHouse Sink: useWeightedWrite must be enabled after writeLocalTable is enabled.");

    // 当前仅支持 JSONEachRow
    Assert.isTrue(
        dataFormat == ClickHouseFormat.JSONEachRow,
        "ClickHouse Sink: only JSONEachRow is supported for now.");
  }
}
