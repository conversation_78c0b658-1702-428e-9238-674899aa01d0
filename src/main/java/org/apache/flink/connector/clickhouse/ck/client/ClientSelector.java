package org.apache.flink.connector.clickhouse.ck.client;

import com.clickhouse.client.api.Client;
import com.clickhouse.client.api.query.GenericRecord;
import java.io.Closeable;
import java.io.IOException;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.clickhouse.ck.config.ConnectionOptions;
import org.apache.flink.connector.clickhouse.ck.entity.ClusterInfo;
import org.apache.flink.connector.clickhouse.ck.strategy.DiskSpaceWeightedStrategy;
import org.apache.flink.connector.clickhouse.ck.strategy.NodeSelectionStrategy;
import org.apache.flink.connector.clickhouse.ck.strategy.RoundRobinStrategy;
import org.apache.flink.connector.clickhouse.utils.ClientUtil;

/** ClickHouse客户端选择器。 根据配置选择合适的客户端，支持根据节点磁盘空间进行加权选择，支持连接失败时的重试和故障转移。 */
@Slf4j
public class ClientSelector implements Closeable, Serializable {

  /** 默认健康检查间隔（毫秒） */
  private static final long DEFAULT_HEALTH_CHECK_INTERVAL_MS = 30000;

  /** 默认最大重试次数 */
  private static final int DEFAULT_MAX_RETRIES = 3;

  /** 默认重试间隔（毫秒） */
  private static final long DEFAULT_RETRY_INTERVAL_MS = 1000;

  @Serial private static final long serialVersionUID = -3461232080748359775L;

  /** 连接选项 */
  private final ConnectionOptions connectionOptions;

  /** 集群信息 */
  @Getter private final ClusterInfo clusterInfo;

  /** 默认客户端 */
  @Getter private NodeClient defaultClient;

  /** 节点选择策略 */
  private NodeSelectionStrategy selectionStrategy;

  /** 最大重试次数 */
  private final int maxRetries;

  /** 重试间隔（毫秒） */
  private final long retryIntervalMs;

  /** 健康检查定时器 */
  private transient ScheduledExecutorService healthCheckScheduler;

  /** 是否已关闭 */
  private volatile boolean closed = false;

  /**
   * 创建一个新的ClickHouse客户端选择器
   *
   * @param connectionOptions 连接选项
   * @param healthCheckIntervalMs 健康检查间隔（毫秒）
   * @param maxRetries 最大重试次数
   * @param retryIntervalMs 重试间隔（毫秒）
   */
  public ClientSelector(
      ConnectionOptions connectionOptions,
      long healthCheckIntervalMs,
      int maxRetries,
      long retryIntervalMs) {
    this.connectionOptions = Objects.requireNonNull(connectionOptions, "连接选项不能为空");
    this.maxRetries = maxRetries;
    this.retryIntervalMs = retryIntervalMs;

    // 创建默认客户端
    String primaryHost = connectionOptions.getHosts().split(",")[0].trim();
    int port = connectionOptions.getPort();

    try {
      Client client =
          ClientUtil.createClickHouseClient(
              primaryHost,
              port,
              connectionOptions.getUsername(),
              connectionOptions.getPassword(),
              connectionOptions.isUseSSL(),
              connectionOptions.getClientCertPath(),
              connectionOptions.getClientKeyPath(),
              connectionOptions.getAdditionalOptions());

      this.defaultClient = new NodeClient(primaryHost, port, client);
    } catch (Exception e) {
      throw new RuntimeException("创建默认客户端失败", e);
    }

    // 初始化集群信息
    String clusterName = connectionOptions.getCluster();
    this.clusterInfo = new ClusterInfo(clusterName, connectionOptions);

    // 选择节点选择策略
    if (tableOptions.isUseWeightedWrite()) {
      this.selectionStrategy = new DiskSpaceWeightedStrategy();
      log.info("使用基于磁盘空间的加权选择策略");
    } else {
      this.selectionStrategy = new RoundRobinStrategy();
      log.info("使用轮询选择策略");
    }

    // 启动健康检查
    if (healthCheckIntervalMs > 0) {
      startHealthCheck(healthCheckIntervalMs);
    }
  }

  /**
   * 使用默认参数创建ClickHouse客户端选择器
   *
   * @param connectionOptions 连接选项
   */
  public ClientSelector(ConnectionOptions connectionOptions) {
    this(
        connectionOptions,
        DEFAULT_HEALTH_CHECK_INTERVAL_MS,
        DEFAULT_MAX_RETRIES,
        DEFAULT_RETRY_INTERVAL_MS);
  }

  /**
   * 启动健康检查定时任务
   *
   * @param intervalMs 检查间隔（毫秒）
   */
  private void startHealthCheck(long intervalMs) {
    this.healthCheckScheduler =
        Executors.newSingleThreadScheduledExecutor(
            r -> {
              Thread t = new Thread(r, "clickhouse-client-health-check");
              t.setDaemon(true);
              return t;
            });

    healthCheckScheduler.scheduleAtFixedRate(
        this::performHealthCheck, intervalMs, intervalMs, TimeUnit.MILLISECONDS);
  }

  /** 执行健康检查 */
  private void performHealthCheck() {
    if (closed) {
      return;
    }

    try {
      // 检查默认客户端
      if (!defaultClient.healthCheck() && !clusterInfo.getAvailableNodeClients().isEmpty()) {
        // 如果默认客户端不可用，尝试从可用节点中选择一个作为新的默认客户端
        NodeClient newDefault = selectionStrategy.selectNode(clusterInfo.getAvailableNodeClients());
        if (newDefault != null) {
          defaultClient = newDefault;
          log.info("默认客户端已切换到: {}:{}", newDefault.getHost(), newDefault.getPort());
        }
      }
    } catch (Exception e) {
      log.error("执行健康检查时出错", e);
    }
  }

  /**
   * 获取一个客户端实例。 根据配置的选择策略，选择一个合适的客户端。 如果没有可用的节点客户端，则返回默认客户端。
   *
   * @return 选择的客户端
   */
  public Client getClient() {
    if (closed) {
      throw new IllegalStateException("客户端选择器已关闭");
    }

    List<NodeClient> availableClients = clusterInfo.getAvailableNodeClients();

    if (availableClients.isEmpty()) {
      log.warn("没有可用的节点客户端，使用默认客户端");
      return defaultClient.getClient();
    }

    // 根据策略选择节点
    NodeClient selectedClient = selectionStrategy.selectNode(availableClients);
    if (selectedClient == null) {
      log.warn("节点选择策略返回null，使用默认客户端");
      return defaultClient.getClient();
    }

    log.debug(
        "选择节点 {}:{} (权重: {})",
        selectedClient.getHost(),
        selectedClient.getPort(),
        selectedClient.getWeight());
    return selectedClient.getClient();
  }

  /**
   * 执行查询并处理结果，支持自动重试。
   *
   * @param queryFunction 查询函数，接受客户端参数并返回查询结果
   * @param <T> 结果类型
   * @return 查询结果
   * @throws Exception 如果查询失败且重试耗尽
   */
  public <T> T executeQuery(ClientQueryFunction<T> queryFunction) throws Exception {
    int retries = 0;
    Exception lastException = null;

    while (retries <= maxRetries) {
      Client client = null;
      try {
        client = getClient();
        return queryFunction.apply(client);
      } catch (Exception e) {
        lastException = e;
        log.warn("执行查询失败 (重试 {}/{}): {}", retries, maxRetries, e.getMessage());

        // 标记客户端失败，客户端选择器将在下一次选择时避免使用
        if (client != null) {
          for (NodeClient nodeClient : clusterInfo.getAllNodeClients()) {
            if (nodeClient.getClient() == client) {
              nodeClient.markFailure();
              break;
            }
          }
        }

        retries++;
        if (retries <= maxRetries) {
          try {
            Thread.sleep(retryIntervalMs);
          } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            throw new IOException("重试过程中线程被中断", ie);
          }
        }
      }
    }

    throw new IOException("执行查询失败，已重试 " + maxRetries + " 次", lastException);
  }

  /**
   * 执行语句，支持自动重试。
   *
   * @param statementConsumer 语句消费者，接受客户端并执行语句
   * @throws Exception 如果执行失败且重试耗尽
   */
  public void executeStatement(Consumer<Client> statementConsumer) throws Exception {
    executeQuery(
        client -> {
          statementConsumer.accept(client);
          return null;
        });
  }

  /**
   * 获取所有磁盘空间信息
   *
   * @return 节点磁盘空间信息，键为节点地址，值为空闲空间（字节）
   */
  public List<GenericRecord> getDiskSpaceRecords() throws Exception {
    return executeQuery(
        client -> client.queryAll("SELECT name, path, free_space, total_space FROM system.disks"));
  }

  /**
   * 客户端查询函数接口
   *
   * @param <T> 结果类型
   */
  @FunctionalInterface
  public interface ClientQueryFunction<T> {
    /**
     * 应用查询到客户端
     *
     * @param client ClickHouse客户端
     * @return 查询结果
     * @throws Exception 如果查询失败
     */
    T apply(Client client) throws Exception;
  }

  @Override
  public void close() throws IOException {
    if (closed) {
      return;
    }
    closed = true;

    // 关闭健康检查定时器
    if (healthCheckScheduler != null && !healthCheckScheduler.isShutdown()) {
      healthCheckScheduler.shutdown();
      try {
        if (!healthCheckScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
          healthCheckScheduler.shutdownNow();
        }
      } catch (InterruptedException e) {
        healthCheckScheduler.shutdownNow();
        Thread.currentThread().interrupt();
      }
    }

    // 关闭集群信息管理器
    if (clusterInfo != null) {
      clusterInfo.close();
    }

    // 关闭默认客户端
    if (defaultClient != null) {
      defaultClient.close();
    }

    log.info("ClickHouse客户端选择器已关闭");
  }
}
