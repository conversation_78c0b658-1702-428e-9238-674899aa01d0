package org.apache.flink.connector.clickhouse.sink;

import java.io.Serial;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.connector.sink2.Sink;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.api.connector.sink2.WriterInitContext;
import org.apache.flink.connector.clickhouse.config.ClickHouseSinkOptions;

/**
 * ClickHouse Sink 实现，支持批量写入、失败重试和加权写入。
 *
 * @param <IN> 输入记录类型
 */
@Slf4j
@Builder
public class ClickHouseSink<IN, OUT> implements Sink<IN> {

  @Serial private static final long serialVersionUID = -6494253249745869514L;

  private final ClickHouseSinkOptions sinkOptions;
  private final ClickHouseSerializer<IN, OUT> serializer;

  public ClickHouseSink(
      ClickHouseSinkOptions sinkOptions, ClickHouseSerializer<IN, OUT> serializer) {
    sinkOptions.validate();

    this.sinkOptions = sinkOptions;
    this.serializer = serializer;
  }

  @Override
  @Deprecated
  public SinkWriter<IN> createWriter(InitContext context) {
    return createWriter();
  }

  @Override
  public SinkWriter<IN> createWriter(WriterInitContext context) {
    return createWriter();
  }

  private SinkWriter<IN> createWriter() {
    return new ClickHouseSinkWriter<>(sinkOptions, serializer);
  }
}
