package org.apache.flink.connector.clickhouse.sink;

import java.io.Serial;
import java.io.Serializable;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.connector.sink2.SinkWriter;
import org.apache.flink.connector.clickhouse.config.ClickHouseSinkOptions;

/**
 * 实现Sink Writer，用于实际写入数据到ClickHouse。
 *
 * @param <IN> 输入记录类型
 * @param <OUT> 输出记录类型
 * <AUTHOR>
 * @date 2025/5/15 10:12:00
 */
@Slf4j
public class ClickHouseSinkWriter<IN, OUT> implements SinkWriter<IN>, Serializable {

  @Serial private static final long serialVersionUID = -712734813466472560L;

  private final transient ClickHouseEmitter<OUT> emitter;
  private final ClickHouseSerializer<IN, OUT> serializer;

  /**
   * 创建一个新的 ClickHouse Sink 写入器。
   *
   * @param sinkOptions Sink 选项
   * @param serializer 序列化器
   */
  public ClickHouseSinkWriter(
      ClickHouseSinkOptions sinkOptions, ClickHouseSerializer<IN, OUT> serializer) {
    this.emitter = ClickHouseEmitter.getInstance(sinkOptions);
    this.serializer = serializer;
  }

  @Override
  @SneakyThrows
  public void write(IN element, Context context) {
    emitter.emit(serializer.serialize(element));
  }

  @Override
  @SneakyThrows
  public void flush(boolean endOfInput) {
    emitter.flush();
  }

  @Override
  @SneakyThrows
  public void close() {
    emitter.close();
  }
}
