package org.apache.flink.connector.clickhouse.ck.strategy;

import org.apache.flink.connector.clickhouse.ck.client.NodeClient;

import java.io.Serial;
import java.util.List;
import java.util.Random;

/** 基于磁盘空间权重的节点选择策略。 根据节点的磁盘空闲空间比例作为权重，空闲空间越大，被选中的概率越高。 */
public class DiskSpaceWeightedStrategy implements NodeSelectionStrategy {

  @Serial private static final long serialVersionUID = -2260557794281179034L;

  /** 随机数生成器 */
  private final Random random = new Random();

  @Override
  public NodeClient selectNode(List<NodeClient> clients) {
    if (clients == null || clients.isEmpty()) {
      return null;
    }

    if (clients.size() == 1) {
      return clients.get(0);
    }

    // 计算总权重
    double totalWeight = clients.stream().mapToDouble(NodeClient::getWeight).sum();

    // 如果总权重为0（可能所有节点的权重都是0），则采用轮询策略
    if (totalWeight <= 0) {
      int randomIndex = random.nextInt(clients.size());
      return clients.get(randomIndex);
    }

    // 生成一个随机值，范围为0到总权重
    double randomWeight = random.nextDouble() * totalWeight;

    // 累加权重直到达到或超过随机值，选择该节点
    double cumulativeWeight = 0;
    for (NodeClient client : clients) {
      cumulativeWeight += client.getWeight();
      if (cumulativeWeight >= randomWeight) {
        return client;
      }
    }

    // 防止浮点数计算精度问题，返回最后一个节点
    return clients.get(clients.size() - 1);
  }

  @Override
  public String getName() {
    return "DiskSpaceWeighted";
  }
}
