package org.apache.flink.connector.clickhouse.client.container;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import com.clickhouse.client.api.query.GenericRecord;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.flink.connector.clickhouse.ck.client.ClientSelector;
import org.apache.flink.connector.clickhouse.ck.config.ConnectionOptions;
import org.apache.flink.connector.clickhouse.config.ClickHouseTableOptions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

/** ClickHouse客户端选择器容器测试。 使用真实的ClickHouse容器测试客户端选择器功能。 */
@Execution(ExecutionMode.SAME_THREAD)
public class ClientSelectorContainerTest extends AbstractClickHouseContainerTest {

  /** 测试创建客户端选择器 */
  @Test
  public void testCreateClientSelector() {
    // 创建连接选项
    ConnectionOptions connectionOptions = createConnectionOptions();

    // 创建表选项
    ClickHouseTableOptions tableOptions = createTableOptions();

    // 创建客户端选择器
    try (ClientSelector selector = new ClientSelector(connectionOptions, tableOptions)) {
      assertNotNull(selector);
      assertNotNull(selector.getDefaultClient());
      assertNotNull(selector.getClusterInfo());
    } catch (Exception e) {
      fail("创建客户端选择器失败", e);
    }
  }

  /** 测试执行查询 */
  @Test
  public void testExecuteQuery() {
    // 创建连接选项和表选项
    ConnectionOptions connectionOptions = createConnectionOptions();
    ClickHouseTableOptions tableOptions = createTableOptions();

    // 创建客户端选择器
    try (ClientSelector selector = new ClientSelector(connectionOptions, tableOptions)) {
      // 执行查询
      List<GenericRecord> records =
          selector.executeQuery(
              client -> client.queryAll("SELECT * FROM test_db.test_table ORDER BY id"));

      // 验证结果
      assertNotNull(records);
      assertEquals(3, records.size());
    } catch (Exception e) {
      fail("执行查询失败", e);
    }
  }

  /** 测试执行语句 */
  @Test
  public void testExecuteStatement() {
    // 创建连接选项和表选项
    ConnectionOptions connectionOptions = createConnectionOptions();
    ClickHouseTableOptions tableOptions = createTableOptions();

    // 创建客户端选择器
    try (ClientSelector selector = new ClientSelector(connectionOptions, tableOptions)) {
      // 执行插入语句
      selector.executeStatement(
          client ->
              client.queryAll(
                  "INSERT INTO test_db.test_health_check (id, status, checked_at) VALUES (2, 'RUNNING', now())"));

      // 验证插入成功
      List<GenericRecord> records =
          selector.executeQuery(
              client -> client.queryAll("SELECT * FROM test_db.test_health_check WHERE id = 2"));

      assertNotNull(records);
      assertFalse(records.isEmpty());
    } catch (Exception e) {
      fail("执行语句失败", e);
    }
  }

  /** 测试获取ClickHouse节点磁盘空间 */
  @Test
  public void testGetDiskSpaceRecords() {
    // 创建连接选项和表选项
    ConnectionOptions connectionOptions = createConnectionOptions();
    ClickHouseTableOptions tableOptions = createTableOptions();

    // 创建客户端选择器
    try (ClientSelector selector = new ClientSelector(connectionOptions, tableOptions)) {
      // 获取磁盘空间记录
      List<GenericRecord> diskSpaceRecords = selector.getDiskSpaceRecords();

      // 验证结果
      assertNotNull(diskSpaceRecords);
      // 注意：磁盘空间记录可能为空，因为这依赖于ClickHouse服务器的实际配置
    } catch (Exception e) {
      fail("获取磁盘空间记录失败", e);
    }
  }

  /** 创建连接选项 */
  private ConnectionOptions createConnectionOptions() {
    return ConnectionOptions.builder()
        .hosts(CLICKHOUSE_CONTAINER.getHost())
        .port(CLICKHOUSE_CONTAINER.getFirstMappedPort())
        .username(CLICKHOUSE_CONTAINER.getUsername())
        .password(CLICKHOUSE_CONTAINER.getPassword())
        .useSSL(false)
        .build();
  }

  /** 创建表选项 */
  private ClickHouseTableOptions createTableOptions() {
    Map<String, String> additionalOptions = new HashMap<>();
    additionalOptions.put("batch_size", "1000");

    return ClickHouseTableOptions.builder()
        .database("test_db")
        .name("test_table")
        .cluster("") // 单机模式，无集群
        .useWeightedWrite(false)
        .build();
  }
}
