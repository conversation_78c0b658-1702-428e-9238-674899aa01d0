package org.apache.flink.connector.clickhouse.client.container;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.clickhouse.client.api.query.GenericRecord;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

/** ClickHouse节点客户端容器测试。 使用真实的ClickHouse容器进行客户端功能测试。 */
@Execution(ExecutionMode.SAME_THREAD)
public class NodeClientContainerTest extends AbstractClickHouseContainerTest {

  /** 测试客户端连接和基本查询 */
  @Test
  public void testBasicQuery() {
    // 执行简单查询
    List<GenericRecord> records = executeQuery("SELECT * FROM test_db.test_table ORDER BY id");

    // 验证结果
    assertNotNull(records);
    assertEquals(3, records.size());
    assertEquals(1L, Long.parseLong(records.get(0).getString("id")));
    assertEquals("test1", records.get(0).getString("name"));
  }

  /** 测试健康检查功能 */
  @Test
  public void testHealthCheck() {
    // 正常状态下的健康检查
    assertTrue(nodeClient.healthCheck());
    assertTrue(nodeClient.isAvailable());
  }

  /** 测试健康检查失败计数和可用性标记 */
  @Test
  public void testFailureCount() {
    // 初始状态
    assertTrue(nodeClient.isAvailable());

    // 模拟失败
    nodeClient.markFailure();
    assertTrue(nodeClient.isAvailable()); // 第一次失败，仍然可用

    nodeClient.markFailure();
    assertTrue(nodeClient.isAvailable()); // 第二次失败，仍然可用

    nodeClient.markFailure();
    assertFalse(nodeClient.isAvailable()); // 第三次失败，标记为不可用

    // 恢复
    nodeClient.markAvailable();
    assertTrue(nodeClient.isAvailable());
  }

  /** 测试权重更新 */
  @Test
  public void testWeightUpdate() {
    assertEquals(1.0, nodeClient.getWeight()); // 默认权重

    // 更新权重
    nodeClient.updateWeight(2.5);
    assertEquals(2.5, nodeClient.getWeight());
  }

  /** 测试插入数据 */
  @Test
  public void testInsertData() {
    // 插入测试数据
    executeUpdate("INSERT INTO test_db.test_health_check (id, status, checked_at) VALUES (1, 'ok', now())");

    // 验证插入成功
    List<GenericRecord> records =
        executeQuery("SELECT * FROM test_db.test_health_check WHERE id = 1");
    assertNotNull(records);
    assertFalse(records.isEmpty());
    assertEquals("ok", records.get(0).getString("status"));
  }

  /** 测试客户端toString方法 */
  @Test
  public void testToString() {
    String clientString = nodeClient.toString();
    assertNotNull(clientString);
    assertTrue(clientString.contains("NodeClient"));
    assertTrue(clientString.contains(CLICKHOUSE_CONTAINER.getHost()));
  }
}
