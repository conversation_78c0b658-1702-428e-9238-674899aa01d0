package org.apache.flink.connector.clickhouse.client.container;

import com.clickhouse.client.api.Client;
import com.clickhouse.client.api.query.GenericRecord;
import com.clickhouse.jdbc.Driver;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.connector.clickhouse.ck.client.NodeClient;
import org.apache.flink.connector.clickhouse.utils.ClientUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.testcontainers.clickhouse.ClickHouseContainer;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

/** ClickHouse容器测试的抽象基类。 提供了ClickHouse容器的初始化、配置和管理功能。 */
@Slf4j
@Testcontainers
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public abstract class AbstractClickHouseContainerTest {

  static {
    try {
      Class.forName(Driver.class.getName());
    } catch (ClassNotFoundException e) {
      throw new RuntimeException("ClickHouse JDBC驱动加载失败", e);
    }
  }

  /** ClickHouse容器镜像名称 */
  private static final String CLICKHOUSE_IMAGE = "clickhouse/clickhouse-server";

  /** ClickHouse容器 */
  @Container
  protected static final ClickHouseContainer CLICKHOUSE_CONTAINER =
      new ClickHouseContainer(DockerImageName.parse(CLICKHOUSE_IMAGE))
          .withInitScript("init-clickhouse.sql")
          .withLogConsumer(new Slf4jLogConsumer(log))
          .withEnv("CLICKHOUSE_USER", "clickhouse")
          .withEnv("CLICKHOUSE_PASSWORD", "clickhouse")
          .withEnv("CLICKHOUSE_DB", "default");

  /** ClickHouse客户端 */
  protected Client clickhouseClient;

  /** ClickHouse节点客户端 */
  protected NodeClient nodeClient;

  /** 启动ClickHouse容器并初始化客户端 */
  @BeforeAll
  public void startContainer() {
    // 启动容器
    CLICKHOUSE_CONTAINER.start();
    log.info("ClickHouse容器已启动，JDBC URL: {}", CLICKHOUSE_CONTAINER.getJdbcUrl());

    // 创建客户端
    createClient();

    // 验证数据库初始化
    validateDatabaseInit();
  }

  /** 关闭容器和客户端 */
  @AfterAll
  public void stopContainer() {
    try {
      if (nodeClient != null) {
        nodeClient.close();
      }
    } catch (IOException e) {
      log.error("关闭客户端时发生错误", e);
    }

    // 停止容器
    CLICKHOUSE_CONTAINER.stop();
    log.info("ClickHouse容器已停止");
  }

  /** 创建ClickHouse客户端 */
  private void createClient() {
    try {
      // 获取容器的主机和端口
      String host = CLICKHOUSE_CONTAINER.getHost();
      int port = CLICKHOUSE_CONTAINER.getFirstMappedPort();

      // 创建客户端
      Map<String, String> options = new HashMap<>();
      clickhouseClient =
          ClientUtil.createClickHouseClient(
              host,
              port,
              CLICKHOUSE_CONTAINER.getUsername(),
              CLICKHOUSE_CONTAINER.getPassword(),
              false,
              null,
              null,
              options);

      // 创建节点客户端
      nodeClient = new NodeClient(host, port, clickhouseClient);
      log.info("已创建ClickHouse客户端: {}:{}", host, port);
    } catch (Exception e) {
      throw new RuntimeException("创建ClickHouse客户端失败", e);
    }
  }

  /** 验证数据库是否正确初始化 */
  protected void validateDatabaseInit() {
    try {
      // 验证表是否存在
      List<GenericRecord> records = clickhouseClient.queryAll("SHOW TABLES FROM test_db");
      log.info("test_db 数据库中的表:");
      for (GenericRecord record : records) {
        log.info(" - {}", record.getString("name"));
      }

      // 验证测试数据
      List<GenericRecord> testData =
          clickhouseClient.queryAll("SELECT COUNT(*) as count FROM test_db.test_table");
      log.info("test_table 中的数据条数: {}", testData.get(0).getString("count"));

      log.info("数据库初始化验证完成");
    } catch (Exception e) {
      log.error("验证数据库初始化失败", e);
    }
  }


  /**
   * 执行SQL查询并返回结果
   *
   * @param sql SQL查询语句
   * @return 查询结果
   */
  protected List<GenericRecord> executeQuery(String sql) {
    try {
      return clickhouseClient.queryAll(sql);
    } catch (Exception e) {
      throw new RuntimeException("执行查询失败: " + sql, e);
    }
  }

  /**
   * 执行SQL更新语句
   *
   * @param sql SQL更新语句
   */
  protected void executeUpdate(String sql) {
    try {
      // 对于更新操作，使用queryAll来执行
      clickhouseClient.queryAll(sql);
    } catch (Exception e) {
      throw new RuntimeException("执行更新失败: " + sql, e);
    }
  }
}
