-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS test_db ON CLUSTER replicated;

-- 在所有节点上创建本地表
CREATE TABLE IF NOT EXISTS test_db.test_table ON CLUSTER replicated (
    id UInt64,
    name String,
    value Float64,
    created_at DateTime
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{cluster}/{shard}/test_db/test_table', '{replica}')
ORDER BY id;

-- 创建系统健康检查表
CREATE TABLE IF NOT EXISTS test_db.test_health_check ON CLUSTER replicated (
    id UInt64,
    status String,
    checked_at DateTime
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{cluster}/{shard}/test_db/test_health_check', '{replica}')
ORDER BY id;

-- 创建分布式表
CREATE TABLE IF NOT EXISTS test_db.test_distributed_table ON CLUSTER replicated (
    id UInt64,
    name String,
    value Float64,
    created_at DateTime
) ENGINE = Distributed('replicated', test_db, test_table, rand());

-- 插入一些测试数据 (只在一个节点上执行即可，复制会自动进行)
INSERT INTO test_db.test_table (id, name, value, created_at) VALUES
    (1, 'test1', 10.5, now()),
    (2, 'test2', 20.1, now()),
    (3, 'test3', 30.7, now());

-- 插入健康检查测试数据
INSERT INTO test_db.test_health_check (id, status, checked_at) VALUES
    (1, 'ok', now()),
    (2, 'warning', now());

-- 在分布式表中插入数据
INSERT INTO test_db.test_distributed_table (id, name, value, created_at) VALUES
    (101, 'dist1', 101.5, now()),
    (102, 'dist2', 102.1, now()),
    (103, 'dist3', 103.7, now());
