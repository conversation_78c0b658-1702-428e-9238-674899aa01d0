#!/bin/bash
set -e

echo "ClickHouse服务已经通过健康检查，开始初始化..."

# 等待ZooKeeper服务就绪
echo "等待ZooKeeper服务就绪..."
sleep 15

# 执行初始化SQL脚本
echo "执行SQL初始化脚本..."
clickhouse-client --host clickhouse-node1 --user default --password "" --multiquery < /docker-entrypoint-initdb.d/init-db.sql

echo "数据库初始化完成！"
echo "验证初始化结果..."

# 验证数据已经成功写入
ROWS=$(clickhouse-client --host clickhouse-node1 --user default --password "" --query "SELECT count() FROM test_db.test_distributed_table")
echo "初始化的数据行数: $ROWS"

if [ "$ROWS" -gt 0 ]; then
    # 检查所有节点上的数据
    echo "验证所有节点上的数据..."
    NODE1_ROWS=$(clickhouse-client --host clickhouse-node1 --user default --password "" --query "SELECT count() FROM test_db.test_table")
    echo "节点1数据行数: $NODE1_ROWS"

    NODE2_ROWS=$(clickhouse-client --host clickhouse-node2 --user default --password "" --query "SELECT count() FROM test_db.test_table")
    echo "节点2数据行数: $NODE2_ROWS"

    NODE3_ROWS=$(clickhouse-client --host clickhouse-node3 --user default --password "" --query "SELECT count() FROM test_db.test_table")
    echo "节点3数据行数: $NODE3_ROWS"

    echo "初始化成功！"
    exit 0
else
    echo "初始化似乎未成功，请检查日志"
    exit 1
fi
