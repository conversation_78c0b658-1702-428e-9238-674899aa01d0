version: '3'
services:
  clickhouse-node1:
    image: clickhouse/clickhouse-server
    container_name: clickhouse-node1
    hostname: clickhouse-node1
    ports:
      - 18123:8123
      - 19000:9000
    volumes:
      - ./node1/config.xml:/etc/clickhouse-server/config.d/config.xml
      - ./node1/keeper.xml:/etc/clickhouse-server/config.d/keeper.xml
      - ./node1/users.xml:/etc/clickhouse-server/users.d/users.xml
    networks:
      - clickhouse-network
    healthcheck:
      test: ["CMD", "clickhouse-client", "--query", "SELECT 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s

  clickhouse-node2:
    image: clickhouse/clickhouse-server
    container_name: clickhouse-node2
    hostname: clickhouse-node2
    ports:
      - 28123:8123
      - 29000:9000
    volumes:
      - ./node2/config.xml:/etc/clickhouse-server/config.d/config.xml
      - ./node2/keeper.xml:/etc/clickhouse-server/config.d/keeper.xml
      - ./node2/users.xml:/etc/clickhouse-server/users.d/users.xml
    depends_on:
      - clickhouse-node1
    networks:
      - clickhouse-network
    healthcheck:
      test: ["CMD", "clickhouse-client", "--query", "SELECT 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s

  clickhouse-node3:
    image: clickhouse/clickhouse-server
    container_name: clickhouse-node3
    hostname: clickhouse-node3
    ports:
      - 38123:8123
      - 39000:9000
    volumes:
      - ./node3/config.xml:/etc/clickhouse-server/config.d/config.xml
      - ./node3/keeper.xml:/etc/clickhouse-server/config.d/keeper.xml
      - ./node3/users.xml:/etc/clickhouse-server/users.d/users.xml
    depends_on:
      - clickhouse-node1
    networks:
      - clickhouse-network
    healthcheck:
      test: ["CMD", "clickhouse-client", "--query", "SELECT 1"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s

  clickhouse-init:
    image: clickhouse/clickhouse-server
    container_name: clickhouse-init
    volumes:
      - ./init-scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./init-scripts/init.sh:/docker-entrypoint-initdb.d/init.sh
    depends_on:
      clickhouse-node1:
        condition: service_healthy
      clickhouse-node2:
        condition: service_healthy
      clickhouse-node3:
        condition: service_healthy
    entrypoint: ["/docker-entrypoint-initdb.d/init.sh"]
    networks:
      - clickhouse-network
    restart: on-failure

networks:
  clickhouse-network:
    driver: bridge
