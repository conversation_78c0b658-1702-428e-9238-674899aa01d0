<clickhouse replace="true">
    <logger>
        <level>information</level>
        <log>console</log>
        <errorlog>console</errorlog>
    </logger>
    <display_name>clickhouse</display_name>
    <listen_host>0.0.0.0</listen_host>
    <http_port>8123</http_port>
    <tcp_port>9000</tcp_port>
    <timezone>Asia/Shanghai</timezone>

    <user_directories>
        <users_xml>
            <path>users.xml</path>
        </users_xml>
        <local_directory>
            <path>/var/lib/clickhouse/access/</path>
        </local_directory>
    </user_directories>

    <distributed_ddl>
        <path>/clickhouse/task_queue/ddl</path>
    </distributed_ddl>

    <zookeeper>
        <node>
            <host>clickhouse-node1</host>
            <port>9181</port>
        </node>
        <node>
            <host>clickhouse-node2</host>
            <port>9181</port>
        </node>
        <node>
            <host>clickhouse-node3</host>
            <port>9181</port>
        </node>
    </zookeeper>

    <remote_servers>
        <!-- User-specified clusters -->
        <replicated>
            <shard>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>clickhouse-node1</host>
                    <port>9000</port>
                </replica>
                <replica>
                    <host>clickhouse-node2</host>
                    <port>9000</port>
                </replica>
            </shard>
            <shard>
                <internal_replication>true</internal_replication>
                <replica>
                    <host>clickhouse-node3</host>
                    <port>9000</port>
                </replica>
            </shard>
        </replicated>
    </remote_servers>

    <macros>
        <installation>clickhouse-node2</installation>
        <cluster>replicated</cluster>
        <shard>0</shard>
        <replica>clickhouse-node2</replica>
    </macros>
</clickhouse>
