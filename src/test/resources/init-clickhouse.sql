-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS test_db;

-- 切换到测试数据库
USE test_db;

-- 创建测试表
CREATE TABLE IF NOT EXISTS test_table (
    id UInt64,
    name String,
    value Float64,
    created_at DateTime
) ENGINE = MergeTree()
ORDER BY id;

-- 插入一些测试数据
INSERT INTO test_table (id, name, value, created_at) VALUES
    (1, 'test1', 10.5, now()),
    (2, 'test2', 20.1, now()),
    (3, 'test3', 30.7, now());

-- 创建一个ReplicatedMergeTree表用于测试复制功能
CREATE TABLE IF NOT EXISTS test_replicated_table (
    id UInt64,
    name String,
    value Float64,
    created_at DateTime
) ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/test_replicated_table', '{replica}')
ORDER BY id;

-- 插入一些测试数据到复制表
INSERT INTO test_replicated_table (id, name, value, created_at) VALUES
    (101, 'replicated1', 101.5, now()),
    (102, 'replicated2', 102.1, now()),
    (103, 'replicated3', 103.7, now());

-- 创建系统健康检查表
CREATE TABLE IF NOT EXISTS test_health_check (
    id UInt64,
    status String,
    checked_at DateTime
) ENGINE = MergeTree()
ORDER BY id;

-- 插入健康检查测试数据
INSERT INTO test_health_check (id, status, checked_at) VALUES
    (1, 'ok', now()),
    (2, 'warning', now());

-- 创建分布式表（连接到集群）
CREATE TABLE IF NOT EXISTS test_distributed_table (
    id UInt64,
    name String,
    value Float64,
    created_at DateTime
) ENGINE = Distributed(test_cluster, test_db, test_replicated_table, rand());

-- 插入一些数据到分布式表
INSERT INTO test_distributed_table (id, name, value, created_at) VALUES
    (201, 'distributed1', 201.5, now()),
    (202, 'distributed2', 202.1, now());
