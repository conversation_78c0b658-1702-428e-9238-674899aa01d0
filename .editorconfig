# EditorConfig helps maintain consistent coding styles for multiple developers
# https://editorconfig.org/

# 顶层配置文件标记
root = true

# 默认设置适用于所有文件
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 4
max_line_length = 100

# Java 文件特定配置
[*.java]
charset = utf-8
indent_style = space
indent_size = 2
continuation_indent_size = 4
max_line_length = 100
ij_java_imports_layout = #*,|,*
ij_java_use_single_class_imports = true
ij_java_class_count_to_use_import_on_demand = 999
ij_java_names_count_to_use_import_on_demand = 999
ij_java_packages_to_use_import_on_demand =

# Markdown 文件不需要去除尾部空格
[*.md]
trim_trailing_whitespace = false
