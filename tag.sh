#!/bin/bash

# 发布脚本 - 自动化版本发布流程
# 功能：
# 1. 从当前分支checkout到pre-release分支
# 2. 提取pom.xml中的版本号，去除-SNAPSHOT后缀
# 3. 使用mvn修改项目版本号
# 4. 提交更改并推送tag
# 5. 切换到main分支并删除pre-release分支

set -e  # 遇到错误立即退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."

    if ! command -v git &> /dev/null; then
        log_error "Git 未安装或不在PATH中"
        exit 1
    fi

    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装或不在PATH中"
        exit 1
    fi

    if ! command -v xmllint &> /dev/null; then
        log_warning "xmllint 未安装，将使用sed作为备选方案"
    fi

    log_success "工具检查完成"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git状态..."

    # 检查是否在Git仓库中
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是Git仓库"
        exit 1
    fi

    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_error "存在未提交的更改，请先提交或暂存"
        git status --porcelain
        exit 1
    fi

    log_success "Git状态检查完成"
}

# 提取版本号
extract_version() {
    log_info "从pom.xml提取版本号..."

    if ! [ -f "pom.xml" ]; then
        log_error "pom.xml 文件不存在"
        exit 1
    fi

    # 尝试使用xmllint提取版本号
    if command -v xmllint &> /dev/null; then
        VERSION=$(xmllint --xpath "string(/project/version)" pom.xml 2>/dev/null)
    else
        # 备选方案：使用sed
        VERSION=$(sed -n '/<version>/p' pom.xml | head -1 | sed 's/.*<version>\(.*\)<\/version>.*/\1/' | tr -d ' \t')
    fi

    if [ -z "$VERSION" ]; then
        log_error "无法从pom.xml提取版本号"
        exit 1
    fi

    log_info "当前版本: $VERSION"

    # 去除-SNAPSHOT后缀
    RELEASE_VERSION=${VERSION%-SNAPSHOT}

    if [ "$VERSION" = "$RELEASE_VERSION" ]; then
        log_warning "版本号中没有-SNAPSHOT后缀: $VERSION"
        read -p "是否继续使用当前版本号? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi

    log_success "发布版本号: $RELEASE_VERSION"
}

# 切换到pre-release分支
checkout_prerelease() {
    log_info "切换到pre-release分支..."

    # 获取当前分支名
    CURRENT_BRANCH=$(git branch --show-current)
    log_info "当前分支: $CURRENT_BRANCH"

    # 检查pre-release分支是否存在
    if git show-ref --verify --quiet refs/heads/pre-release; then
        log_info "pre-release分支已存在，切换到该分支"
        git checkout pre-release
    else
        log_info "创建并切换到pre-release分支"
        git checkout -b pre-release
    fi

    log_success "已切换到pre-release分支"
}

# 更新版本号
update_version() {
    log_info "更新项目版本号为: $RELEASE_VERSION"

    # 使用Maven versions插件更新版本号
    if ! mvn versions:set -DnewVersion="$RELEASE_VERSION" -DgenerateBackupPoms=false; then
        log_error "Maven版本更新失败"
        exit 1
    fi

    log_success "版本号更新完成"
}

# 提交更改
commit_changes() {
    log_info "提交版本更改..."

    # 添加pom.xml到暂存区
    git add pom.xml

    # 检查是否有其他需要提交的文件
    if git diff --cached --quiet; then
        log_warning "没有检测到版本更改，可能版本号已经是发布版本"
        return
    fi

    # 提交更改
    COMMIT_MESSAGE="release $RELEASE_VERSION"
    git commit -m "$COMMIT_MESSAGE"

    log_success "提交完成: $COMMIT_MESSAGE"
}

# 创建并推送tag
create_and_push_tag() {
    log_info "创建并推送tag..."

    # 创建tag
    TAG_NAME="v$RELEASE_VERSION"
    git tag -a "$TAG_NAME" -m "Release version $RELEASE_VERSION"

    # 推送tag
    git push origin "$TAG_NAME"

    log_success "Tag $TAG_NAME 已创建并推送"
}

# 切换到main分支并清理
cleanup() {
    log_info "切换到main分支..."

    # 切换到main分支
    if git show-ref --verify --quiet refs/heads/main; then
        git checkout main
        log_success "已切换到main分支"
        MAIN_BRANCH="main"
    elif git show-ref --verify --quiet refs/heads/master; then
        git checkout master
        log_success "已切换到master分支"
        MAIN_BRANCH="master"
    else
        log_warning "未找到main或master分支，保持在当前分支"
        return
    fi

    # 删除本地pre-release分支
    log_info "删除本地pre-release分支..."
    git branch -D pre-release

    log_success "清理完成"
}

# 更新main分支版本号
update_main_version() {
    log_info "准备更新main分支版本号..."

    # 询问用户输入新的版本号
    echo
    log_info "当前发布版本: $RELEASE_VERSION"
    read -p "请输入main分支的新版本号 (例如: ${RELEASE_VERSION%.*}.$((${RELEASE_VERSION##*.} + 1))-SNAPSHOT): " NEW_MAIN_VERSION

    if [ -z "$NEW_MAIN_VERSION" ]; then
        log_warning "未输入版本号，跳过main分支版本更新"
        return
    fi

    log_info "将main分支版本号更新为: $NEW_MAIN_VERSION"

    # 使用Maven versions插件更新版本号
    if ! mvn versions:set -DnewVersion="$NEW_MAIN_VERSION" -DgenerateBackupPoms=false; then
        log_error "Maven版本更新失败"
        return
    fi

    # 提交更改
    git add pom.xml

    # 检查是否有更改
    if git diff --cached --quiet; then
        log_warning "没有检测到版本更改"
        return
    fi

    # 提交更改
    COMMIT_MESSAGE="start $NEW_MAIN_VERSION"
    git commit -m "$COMMIT_MESSAGE"

    # 推送到远端
    git push origin "$MAIN_BRANCH"

    log_success "main分支版本更新完成: $NEW_MAIN_VERSION"
    log_success "提交消息: $COMMIT_MESSAGE"
}

# 主函数
main() {
    log_info "开始发布流程..."

    check_prerequisites
    check_git_status
    extract_version

    # 确认发布
    echo
    log_warning "即将执行以下操作:"
    echo "  1. 切换到pre-release分支"
    echo "  2. 将版本号从 $VERSION 更新为 $RELEASE_VERSION"
    echo "  3. 提交更改"
    echo "  4. 创建并推送tag: v$RELEASE_VERSION"
    echo "  5. 切换回main分支并删除pre-release分支"
    echo
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi

    checkout_prerelease
    update_version
    commit_changes
    create_and_push_tag
    cleanup

    echo
    log_success "发布流程完成!"
    log_success "发布版本: $RELEASE_VERSION"
    log_success "Tag: v$RELEASE_VERSION"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
