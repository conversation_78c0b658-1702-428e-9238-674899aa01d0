# Flink ClickHouse Connector

基于Flink 1.20和ClickHouse的连接器实现，支持将Flink处理的数据写入ClickHouse本地表。

## 项目依赖

- Java 17
- Apache Flink 1.20.0
- ClickHouse Client 0.8.5
- Lombok
- FastJSON2

## 项目结构

```
flink-clickhouse-connector/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── org/
│   │   │       └── apache/
│   │   │           └── flink/
│   │   │               └── connector/
│   │   │                   └── clickhouse/
│   │   └── resources/
│   └── test/
│       ├── java/
│       │   └── org/
│       │       └── apache/
│       │           └── flink/
│       │               └── connector/
│       │                   └── clickhouse/
│       └── resources/
└── pom.xml
```

## 功能特点

- 支持Flink批处理和流处理模式
- 支持写入ClickHouse本地表
- 支持根据ClickHouse服务器空闲磁盘大小进行权重写入
- 支持失败重试
- 支持自定义类型转换
- 基于ClickHouse高性能客户端

## 如何使用

### 添加依赖

```xml
<dependency>
    <groupId>org.apache.flink</groupId>
    <artifactId>flink-clickhouse-connector</artifactId>
    <version>1.0-SNAPSHOT</version>
</dependency>
```

### Sink使用示例

以下是一个简单的示例，展示如何使用ClickHouse Sink写入数据：

```java
// 创建数据流
DataStream<Person> personStream = ...;

// 配置ClickHouse连接
ClickHouseConnectionOptions connectionOptions = ClickHouseConnectionOptions.builder()
    .withHosts("localhost")
    .withPort(8123)
    .withDatabase("default")
    .withUsername("default")
    .withPassword("")
    .build();

// 配置Sink选项
ClickHouseSinkOptions sinkOptions = ClickHouseSinkOptions.builder()
    .withTableName("persons")
    .withBatchSize(1000)
    .withBatchIntervalMs(1000)
    .withEnableCompression(true)
    .withMaxRetries(3)
    .withRetryIntervalMs(1000)
    .withUseWeightedWrite(false)
    .build();

// 创建序列化器 (支持POJO和JSON两种模式)
PojoClickHouseSerializer<Person> serializer = new PojoClickHouseSerializer<>(Person.class);
// 或者使用JSON序列化器
// JsonClickHouseSerializer<Person> serializer = new JsonClickHouseSerializer<>();

// 构建并添加Sink
personStream.

addSink(
    ClickHouseSinkBuilder .<Person>builder()
        .

setConnectionOptions(connectionOptions)
        .

setSinkOptions(sinkOptions)
        .

setSerializer(serializer)
        .

build()
);
```

### 表创建示例

使用此连接器前，需要在ClickHouse中创建相应的表，例如：

```sql
CREATE TABLE default.persons (
    id UInt32,
    name String,
    age UInt8,
    createTime DateTime
) ENGINE = MergeTree()
ORDER BY id;
```

### 高级功能

#### 根据磁盘空间加权写入

您可以启用加权写入功能，使系统根据ClickHouse集群中各服务器的可用磁盘空间分配写入负载：

```java
ClickHouseSinkOptions sinkOptions = ClickHouseSinkOptions.builder()
    .withTableName("persons")
    // 启用加权写入
    .withUseWeightedWrite(true)
    // 其他选项...
    .build();
```

#### 批处理和失败重试

连接器支持批量写入和失败重试机制，可以通过以下配置调整：

```java
ClickHouseSinkOptions sinkOptions = ClickHouseSinkOptions.builder()
    .withBatchSize(1000) // 每批次写入的记录数
    .withBatchIntervalMs(5000) // 固定时间间隔刷新（毫秒）
    .withMaxRetries(3) // 最大重试次数
    .withRetryIntervalMs(1000) // 重试间隔（毫秒）
    .build();
```

## 构建方式

```bash
mvn clean package
```

## 许可证

待定 
