# ClickHouse 客户端选择器

ClickHouse 客户端选择器是一个可以根据不同策略选择 ClickHouse 节点进行操作的组件。它支持以下功能：

1. 默认使用单个客户端连接
2. 从 ClickHouse 集群获取所有节点信息，为每个节点创建单独的客户端
3. 支持基于节点磁盘空闲空间进行加权写入
4. 支持节点失败时的重试和自动故障转移
5. 支持定时健康检查，自动恢复不可用节点

## 主要组件

- **ClickHouseNodeClient**：单个节点的客户端封装
- **ClickHouseClusterInfo**：集群信息管理，自动刷新节点信息和磁盘空间
- **ClickHouseNodeSelectionStrategy**：节点选择策略接口
  - **RoundRobinStrategy**：轮询策略实现
  - **DiskSpaceWeightedStrategy**：基于磁盘空间的加权策略实现
- **ClickHouseClientSelector**：核心选择器实现

## 使用示例

### 基本用法

```java
// 创建连接选项
ClickHouseConnectionOptions connectionOptions = ClickHouseConnectionOptions.builder()
    .hosts("host1,host2,host3")
    .port(8123)
    .username("default")
    .password("password")
    .useSSL(false)
    .build();

// 创建表选项
ClickHouseTableOptions tableOptions = ClickHouseTableOptions.builder()
    .name("my_table")
    .database("my_database")
    .cluster("my_cluster")
    .useWeightedWrite(true)  // 启用基于磁盘空间的加权写入
    .build();

// 创建客户端选择器
ClickHouseClientSelector selector = ClickHouseUtil.createClientSelector(
    connectionOptions, tableOptions);

// 获取客户端并执行操作
try {
    // 执行查询
    List<GenericRecord> records = selector.executeQuery(client -> 
        client.queryAll("SELECT * FROM my_database.my_table LIMIT 10"));

    // 执行写入操作
    selector.executeStatement(client -> 
        client.write()
              .format(ClickHouseFormat.JSONEachRow)
              .addData("{\"id\":1,\"name\":\"test\"}")
              .table("my_database.my_table")
              .send());
} catch (Exception e) {
    // 处理异常
} finally {
    // 关闭选择器
    selector.close();
}
```

### 获取集群磁盘空间信息

```java
// 获取所有节点的磁盘空间信息
List<GenericRecord> diskInfo = selector.getDiskSpaceRecords();
for (GenericRecord record : diskInfo) {
    String name = record.getString("name");
    String path = record.getString("path");
    long freeSpace = record.getLong("free_space");
    long totalSpace = record.getLong("total_space");
    
    System.out.printf("磁盘: %s, 路径: %s, 剩余空间: %d, 总空间: %d%n", 
        name, path, freeSpace, totalSpace);
}
```

## 配置选项

### 客户端选择器配置

- **healthCheckIntervalMs**：健康检查间隔（毫秒），默认 30000
- **maxRetries**：最大重试次数，默认 3
- **retryIntervalMs**：重试间隔（毫秒），默认 1000

### 集群信息配置

- **refreshIntervalMs**：集群信息刷新间隔（毫秒），默认 60000

## 注意事项

1. 确保表选项中的 `useWeightedWrite` 设置为 `true` 时，对应的表确实分布在集群的不同节点上
2. 在使用完毕后，需要显式调用 `close()` 方法释放资源
3. 每个表应该使用独立的客户端选择器实例 
